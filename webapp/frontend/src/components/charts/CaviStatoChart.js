import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ComposedChart,
  Line
} from 'recharts';
import { Box, Typography, Grid, Paper, Chip } from '@mui/material';

const COLORS = {
  'Installato': '#2e7d32',
  'Da Installare': '#ed6c02',
  'In Progresso': '#1976d2',
  'Spare': '#9c27b0',
  'Sospeso': '#d32f2f'
};

const STATUS_COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  info: '#0288d1',
  error: '#d32f2f'
};

const CaviStatoChart = ({ data }) => {
  if (!data || !data.cavi_per_stato) return null;

  // Prepara dati per i grafici
  const statoData = data.cavi_per_stato.map(stato => ({
    ...stato,
    stato_short: stato.stato?.length > 12 ? stato.stato.substring(0, 12) + '...' : stato.stato,
    color: COLORS[stato.stato] || STATUS_COLORS.info,
    efficienza: stato.metri_teorici > 0 ? (stato.metri_reali / stato.metri_teorici * 100) : 0
  }));

  // Calcola totali
  const totali = statoData.reduce((acc, stato) => {
    acc.cavi += stato.num_cavi || 0;
    acc.metri_teorici += stato.metri_teorici || 0;
    acc.metri_reali += stato.metri_reali || 0;
    return acc;
  }, { cavi: 0, metri_teorici: 0, metri_reali: 0 });

  // Dati per grafico a torta
  const pieData = statoData.map(stato => ({
    name: stato.stato,
    value: stato.num_cavi,
    color: stato.color
  }));

  // Dati per confronto metri
  const metriData = statoData.map(stato => ({
    stato: stato.stato_short,
    stato_full: stato.stato,
    metri_teorici: stato.metri_teorici || 0,
    metri_reali: stato.metri_reali || 0,
    differenza: (stato.metri_reali || 0) - (stato.metri_teorici || 0)
  }));

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null;
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Analisi Cavi per Stato di Installazione
      </Typography>
      
      <Grid container spacing={3}>
        {/* Statistiche Generali */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Riepilogo Generale
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="h6" color="primary">
                    {totali.cavi}
                  </Typography>
                  <Typography variant="body2">Cavi Totali</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="h6" color="info.main">
                    {totali.metri_teorici.toFixed(0)}m
                  </Typography>
                  <Typography variant="body2">Metri Teorici</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                  <Typography variant="h6" color="success.main">
                    {totali.metri_reali.toFixed(0)}m
                  </Typography>
                  <Typography variant="body2">Metri Reali</Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Grafico a torta - Distribuzione Cavi */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 350 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Distribuzione Cavi per Stato
            </Typography>
            <ResponsiveContainer width="100%" height={280}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico a barre - Numero Cavi per Stato */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 350 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Numero Cavi per Stato
            </Typography>
            <ResponsiveContainer width="100%" height={280}>
              <BarChart data={statoData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="stato_short" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="num_cavi" fill={STATUS_COLORS.primary} name="Numero Cavi" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico confronto metri */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2, height: 350 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Confronto Metri Teorici vs Reali per Stato
            </Typography>
            <ResponsiveContainer width="100%" height={280}>
              <BarChart data={metriData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="stato" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="metri_teorici" fill={STATUS_COLORS.info} name="Metri Teorici" />
                <Bar dataKey="metri_reali" fill={STATUS_COLORS.success} name="Metri Reali" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Dettaglio per Stato */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Dettaglio per Stato
            </Typography>
            <Grid container spacing={2}>
              {statoData.map((stato, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                  <Box sx={{ 
                    textAlign: 'center', 
                    p: 2, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 1,
                    borderLeft: `4px solid ${stato.color}`
                  }}>
                    <Chip 
                      label={stato.stato}
                      style={{ backgroundColor: stato.color, color: 'white' }}
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="h6">{stato.num_cavi}</Typography>
                    <Typography variant="body2">cavi</Typography>
                    <Typography variant="body2">
                      Teorici: <strong>{stato.metri_teorici?.toFixed(0)}m</strong>
                    </Typography>
                    <Typography variant="body2">
                      Reali: <strong>{stato.metri_reali?.toFixed(0)}m</strong>
                    </Typography>
                    <Typography variant="body2">
                      Efficienza: <strong>{stato.efficienza?.toFixed(1)}%</strong>
                    </Typography>
                    {stato.metri_reali > stato.metri_teorici && (
                      <Chip 
                        label={`+${(stato.metri_reali - stato.metri_teorici).toFixed(0)}m`}
                        color="warning"
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    )}
                    {stato.metri_reali < stato.metri_teorici && (
                      <Chip 
                        label={`-${(stato.metri_teorici - stato.metri_reali).toFixed(0)}m`}
                        color="error"
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    )}
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>

        {/* Analisi Efficienza */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Analisi Efficienza Installazione
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" gutterBottom>
                  <strong>Stati con Surplus di Metri:</strong>
                </Typography>
                {metriData
                  .filter(stato => stato.differenza > 0)
                  .map((stato, index) => (
                    <Box key={index} sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      p: 1, 
                      mb: 1,
                      border: '1px solid #e0e0e0', 
                      borderRadius: 1,
                      borderLeft: `4px solid ${STATUS_COLORS.success}`
                    }}>
                      <Typography variant="body2">{stato.stato_full}</Typography>
                      <Chip 
                        label={`+${stato.differenza.toFixed(0)}m`}
                        color="success"
                        size="small"
                      />
                    </Box>
                  ))}
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" gutterBottom>
                  <strong>Stati con Deficit di Metri:</strong>
                </Typography>
                {metriData
                  .filter(stato => stato.differenza < 0)
                  .map((stato, index) => (
                    <Box key={index} sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      p: 1, 
                      mb: 1,
                      border: '1px solid #e0e0e0', 
                      borderRadius: 1,
                      borderLeft: `4px solid ${STATUS_COLORS.error}`
                    }}>
                      <Typography variant="body2">{stato.stato_full}</Typography>
                      <Chip 
                        label={`${stato.differenza.toFixed(0)}m`}
                        color="error"
                        size="small"
                      />
                    </Box>
                  ))}
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CaviStatoChart;
