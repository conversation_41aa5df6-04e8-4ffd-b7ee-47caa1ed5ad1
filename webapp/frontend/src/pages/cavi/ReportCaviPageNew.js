import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Timeline as TimelineIcon,
  List as ListIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Refresh as RefreshIcon,
  ArrowBack as ArrowBackIcon,
  DateRange as DateRangeIcon,
  Cable as CableIcon,
  Inventory as InventoryIcon,
  ExpandMore as ExpandMoreIcon,
  ShowChart as ShowChartIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import AdminHomeButton from '../../components/common/AdminHomeButton';
import reportService from '../../services/reportService';

// Import dei componenti grafici
import ProgressChart from '../../components/charts/ProgressChart';
import BobineChart from '../../components/charts/BobineChart';
import BoqChart from '../../components/charts/BoqChart';
import TimelineChart from '../../components/charts/TimelineChart';
import CaviStatoChart from '../../components/charts/CaviStatoChart';

const ReportCaviPageNew = () => {
  const navigate = useNavigate();
  const { cantiereId } = useParams();
  const { user } = useAuth();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [selectedReport, setSelectedReport] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [formData, setFormData] = useState({
    formato: 'video',
    data_inizio: '',
    data_fine: '',
    id_bobina: ''
  });

  // New state to store all report data
  const [reportsData, setReportsData] = useState({
    progress: null,
    boq: null,
    bobine: null,
    caviStato: null,
    bobinaSpecifica: null,
    posaPeriodo: null
  });

  // State per controllo visualizzazione grafici
  const [showCharts, setShowCharts] = useState(true);

  // Load all basic reports on component mount
  useEffect(() => {
    const loadAllReports = async () => {
      setLoading(true);
      try {
        // Create individual promises that handle their own errors
        const progressPromise = reportService.getProgressReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading progress report:', err);
            return { content: null };
          });

        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading BOQ report:', err);
            return { content: null };
          });

        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading bobine report:', err);
            return { content: null };
          });

        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')
          .catch(err => {
            console.error('Error loading cavi stato report:', err);
            return { content: null };
          });

        // Wait for all promises to resolve (they won't reject due to the catch handlers)
        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([
          progressPromise,
          boqPromise,
          bobinePromise,
          caviStatoPromise
        ]);

        // Set the data for each report, even if some are null
        setReportsData({
          progress: progressData.content,
          boq: boqData.content,
          bobine: bobineData.content,
          caviStato: caviStatoData.content,
          bobinaSpecifica: null,
          posaPeriodo: null
        });

        // Only set error to null if we successfully loaded at least one report
        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {
          setError(null);
        } else {
          setError('Errore nel caricamento dei report. Riprova più tardi.');
        }
      } catch (err) {
        // This catch block should rarely be hit due to the individual error handling above
        console.error('Unexpected error loading reports:', err);
        setError('Errore nel caricamento dei report. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    if (cantiereId) {
      loadAllReports();
    }
  }, [cantiereId]);

  // Configurazione dei report disponibili
  const reportTypes = [
    {
      id: 'progress',
      title: 'Report Avanzamento',
      description: 'Panoramica completa dell\'avanzamento dei lavori con metriche di performance e previsioni',
      icon: <AssessmentIcon />,
      color: 'primary',
      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']
    },
    {
      id: 'boq',
      title: 'Bill of Quantities',
      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',
      icon: <ListIcon />,
      color: 'secondary',
      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']
    },
    {
      id: 'bobine',
      title: 'Report Utilizzo Bobine',
      description: 'Analisi completa dell\'utilizzo delle bobine con efficienza e sprechi',
      icon: <InventoryIcon />,
      color: 'success',
      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']
    },
    {
      id: 'bobina-specifica',
      title: 'Report Bobina Specifica',
      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',
      icon: <CableIcon />,
      color: 'info',
      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']
    },
    {
      id: 'posa-periodo',
      title: 'Report Posa per Periodo',
      description: 'Analisi temporale della posa con trend e pattern di lavoro',
      icon: <TimelineIcon />,
      color: 'warning',
      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']
    },
    {
      id: 'cavi-stato',
      title: 'Report Cavi per Stato',
      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',
      icon: <BarChartIcon />,
      color: 'error',
      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']
    }
  ];

  // Nuova funzione per generare report con formato specificato
  const generateReportWithFormat = async (reportType, format) => {
    try {
      setLoading(true);
      setError(null);

      let response;

      switch (reportType) {
        case 'progress':
          response = await reportService.getProgressReport(cantiereId, format);
          break;
        case 'boq':
          response = await reportService.getBillOfQuantities(cantiereId, format);
          break;
        case 'bobine':
          response = await reportService.getBobineReport(cantiereId, format);
          break;
        case 'cavi-stato':
          response = await reportService.getCaviStatoReport(cantiereId, format);
          break;
        case 'bobina-specifica':
          if (!formData.id_bobina) {
            setError('Inserisci l\'ID della bobina');
            return;
          }
          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);
          break;
        case 'posa-periodo':
          if (!formData.data_inizio || !formData.data_fine) {
            setError('Seleziona le date di inizio e fine periodo');
            return;
          }
          response = await reportService.getPosaPerPeriodoReport(
            cantiereId,
            formData.data_inizio,
            formData.data_fine,
            format
          );
          break;
        default:
          throw new Error('Tipo di report non riconosciuto');
      }

      if (format === 'video') {
        // For special reports, update the specific report data
        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {
          setReportsData(prev => ({
            ...prev,
            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content
          }));
        }
        setReportData(response.content);
      } else {
        // Per PDF/Excel, apri il link di download
        if (response.file_url) {
          window.open(response.file_url, '_blank');
        }
      }
    } catch (err) {
      console.error('Errore nella generazione del report:', err);
      setError(err.detail || err.message || 'Errore durante la generazione del report');
    } finally {
      setLoading(false);
    }
  };

  const handleReportSelect = (reportType) => {
    setSelectedReport(reportType);
    setDialogType(reportType.id);

    // Per report che necessitano di parametri aggiuntivi, mostra il dialog
    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {
      // Imposta valori di default per alcuni report
      if (reportType.id === 'posa-periodo') {
        const today = new Date();
        const lastMonth = new Date();
        lastMonth.setMonth(today.getMonth() - 1);

        setFormData({
          ...formData,
          data_inizio: lastMonth.toISOString().split('T')[0],
          data_fine: today.toISOString().split('T')[0]
        });
      }

      setOpenDialog(true);
    } else {
      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'
      generateReportWithFormat(reportType.id, 'video');
    }
  };

  const handleGenerateReport = async () => {
    await generateReportWithFormat(dialogType, formData.formato);
    setOpenDialog(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setError(null);
    setFormData({
      formato: 'video',
      data_inizio: '',
      data_fine: '',
      id_bobina: ''
    });
  };

  const renderReportContent = () => {
    if (!reportData) return null;

    return (
      <Paper sx={{ p: 3, mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            {selectedReport?.title} - {reportData.nome_cantiere}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Export buttons */}
            <Button
              startIcon={<DownloadIcon />}
              onClick={() => generateReportWithFormat(dialogType, 'pdf')}
              variant="outlined"
              size="small"
              color="primary"
            >
              PDF
            </Button>
            <Button
              startIcon={<DownloadIcon />}
              onClick={() => generateReportWithFormat(dialogType, 'excel')}
              variant="outlined"
              size="small"
              color="success"
            >
              Excel
            </Button>
            <Button
              startIcon={<RefreshIcon />}
              onClick={() => setReportData(null)}
              variant="outlined"
              size="small"
            >
              Nuovo Report
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Renderizza il contenuto specifico del report */}
        {dialogType === 'progress' && renderProgressReport(reportData)}
        {dialogType === 'boq' && renderBoqReport(reportData)}
        {dialogType === 'bobine' && renderBobineReport(reportData)}
        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}
        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}
        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}
      </Paper>
    );
  };

  const renderProgressReport = (data) => (
    <Grid container spacing={3}>
      {/* Controllo visualizzazione grafici */}
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={showCharts}
                onChange={(e) => setShowCharts(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ShowChartIcon sx={{ mr: 1 }} />
                Mostra Grafici
              </Box>
            }
          />
        </Box>
      </Grid>

      {/* Grafici */}
      {showCharts && (
        <Grid item xs={12}>
          <ProgressChart data={data} />
        </Grid>
      )}

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Avanzamento Generale</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Typography>Metri Totali: <strong>{data.metri_totali}m</strong></Typography>
                <Typography>Metri Posati: <strong>{data.metri_posati}m</strong></Typography>
                <Typography>Metri Rimanenti: <strong>{data.metri_da_posare}m</strong></Typography>
                <Typography>Avanzamento: <strong>{data.percentuale_avanzamento}%</strong></Typography>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>
      </Grid>

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Cavi</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Typography>Totale Cavi: <strong>{data.totale_cavi}</strong></Typography>
                <Typography>Cavi Posati: <strong>{data.cavi_posati}</strong></Typography>
                <Typography>Cavi Rimanenti: <strong>{data.cavi_rimanenti}</strong></Typography>
                <Typography>Percentuale Cavi: <strong>{data.percentuale_cavi}%</strong></Typography>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>
      </Grid>

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Performance</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>
                {data.giorni_stimati && (
                  <>
                    <Typography>Giorni Stimati: <strong>{data.giorni_stimati} giorni</strong></Typography>
                    <Typography>Data Completamento: <strong>{data.data_completamento}</strong></Typography>
                  </>
                )}
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>
      </Grid>

      {data.posa_recente && data.posa_recente.length > 0 && (
        <Grid item xs={12}>
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Posa Recente</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Card>
                <CardContent>
                  {data.posa_recente.slice(0, 5).map((posa, index) => (
                    <Typography key={index}>
                      {posa.data}: <strong>{posa.metri}m</strong>
                    </Typography>
                  ))}
                </CardContent>
              </Card>
            </AccordionDetails>
          </Accordion>
        </Grid>
      )}
    </Grid>
  );

  const renderBoqReport = (data) => (
    <Grid container spacing={3}>
      {/* Grafici */}
      {showCharts && (
        <Grid item xs={12}>
          <BoqChart data={data} />
        </Grid>
      )}

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Cavi per Tipologia</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {data.cavi_per_tipo?.map((cavo, index) => (
                <Grid item xs={12} md={6} lg={4} key={index}>
                  <Card>
                    <CardContent>
                      <Typography variant="subtitle1">{cavo.tipologia}</Typography>
                      <Typography variant="body2">Sezione: {cavo.sezione}</Typography>
                      <Typography>Cavi: {cavo.num_cavi}</Typography>
                      <Typography>Metri Teorici: {cavo.metri_teorici}m</Typography>
                      <Typography>Metri Reali: {cavo.metri_reali}m</Typography>
                      <Typography>Da Posare: {cavo.metri_da_posare}m</Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Bobine Disponibili</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {data.bobine_per_tipo?.map((bobina, index) => (
                <Grid item xs={12} md={6} lg={4} key={index}>
                  <Card>
                    <CardContent>
                      <Typography variant="subtitle1">{bobina.tipologia}</Typography>
                      <Typography variant="body2">Sezione: {bobina.sezione}</Typography>
                      <Typography>Bobine: {bobina.num_bobine}</Typography>
                      <Typography>Metri Disponibili: {bobina.metri_disponibili}m</Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </Grid>
  );

  const renderBobineReport = (data) => (
    <Grid container spacing={3}>
      {/* Grafici */}
      {showCharts && (
        <Grid item xs={12}>
          <BobineChart data={data} />
        </Grid>
      )}

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">
              Bobine del Cantiere ({data.totale_bobine} totali)
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {data.bobine?.map((bobina, index) => (
                <Grid item xs={12} md={6} lg={4} key={index}>
                  <Card>
                    <CardContent>
                      <Typography variant="subtitle1">{bobina.id_bobina}</Typography>
                      <Typography variant="body2">{bobina.tipologia} - {bobina.sezione}</Typography>
                      <Chip
                        label={bobina.stato}
                        color={bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}
                        size="small"
                        sx={{ mb: 1 }}
                      />
                      <Typography>Metri Totali: {bobina.metri_totali}m</Typography>
                      <Typography>Metri Residui: {bobina.metri_residui}m</Typography>
                      <Typography>Metri Utilizzati: {bobina.metri_utilizzati}m</Typography>
                      <Typography>Utilizzo: {bobina.percentuale_utilizzo}%</Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </Grid>
  );

  const renderBobinaSpecificaReport = (data) => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Dettagli Bobina</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Typography>ID: <strong>{data.bobina?.id_bobina}</strong></Typography>
                <Typography>Tipologia: <strong>{data.bobina?.tipologia}</strong></Typography>
                <Typography>Sezione: <strong>{data.bobina?.sezione}</strong></Typography>
                <Chip
                  label={data.bobina?.stato}
                  color={data.bobina?.stato === 'DISPONIBILE' ? 'success' : 'warning'}
                  sx={{ my: 1 }}
                />
                <Typography>Metri Totali: <strong>{data.bobina?.metri_totali}m</strong></Typography>
                <Typography>Metri Residui: <strong>{data.bobina?.metri_residui}m</strong></Typography>
                <Typography>Metri Utilizzati: <strong>{data.bobina?.metri_utilizzati}m</strong></Typography>
                <Typography>Utilizzo: <strong>{data.bobina?.percentuale_utilizzo}%</strong></Typography>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>
      </Grid>

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">
              Cavi Associati ({data.totale_cavi})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {data.cavi_associati?.map((cavo, index) => (
                    <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                      <Typography variant="body2"><strong>{cavo.id_cavo}</strong></Typography>
                      <Typography variant="caption">
                        {cavo.sistema} - {cavo.utility} - {cavo.tipologia}
                      </Typography>
                      <Typography variant="caption" display="block">
                        Teorici: {cavo.metri_teorici}m | Reali: {cavo.metri_reali}m | Stato: {cavo.stato}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </Grid>
  );

  const renderPosaPeriodoReport = (data) => (
    <Grid container spacing={3}>
      {/* Grafici */}
      {showCharts && (
        <Grid item xs={12}>
          <TimelineChart data={data} />
        </Grid>
      )}

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Statistiche Periodo</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Typography>Periodo: <strong>{data.data_inizio} - {data.data_fine}</strong></Typography>
                <Typography>Totale Metri: <strong>{data.totale_metri_periodo}m</strong></Typography>
                <Typography>Giorni Attivi: <strong>{data.giorni_attivi}</strong></Typography>
                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>
      </Grid>

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Posa Giornaliera</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Card>
              <CardContent>
                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {data.posa_giornaliera?.map((posa, index) => (
                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                      <Typography>{posa.data}</Typography>
                      <Typography><strong>{posa.metri}m</strong></Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </Grid>
  );

  const renderCaviStatoReport = (data) => (
    <Grid container spacing={3}>
      {/* Grafici */}
      {showCharts && (
        <Grid item xs={12}>
          <CaviStatoChart data={data} />
        </Grid>
      )}

      <Grid item xs={12}>
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Cavi per Stato di Installazione</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {data.cavi_per_stato?.map((stato, index) => (
                <Grid item xs={12} md={6} lg={3} key={index}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        <Chip
                          label={stato.stato}
                          color={stato.stato === 'Installato' ? 'success' : 'warning'}
                          sx={{ mb: 1 }}
                        />
                      </Typography>
                      <Typography>Numero Cavi: <strong>{stato.num_cavi}</strong></Typography>
                      <Typography>Metri Teorici: <strong>{stato.metri_teorici}m</strong></Typography>
                      <Typography>Metri Reali: <strong>{stato.metri_reali}m</strong></Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>
      </Grid>
    </Grid>
  );

  const renderDialog = () => (
    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
      <DialogTitle>
        {selectedReport?.title}
      </DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Formato</InputLabel>
              <Select
                value={formData.formato}
                label="Formato"
                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}
              >
                <MenuItem value="video">Visualizza a schermo</MenuItem>
                <MenuItem value="pdf">Download PDF</MenuItem>
                <MenuItem value="excel">Download Excel</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {dialogType === 'bobina-specifica' && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="ID Bobina"
                value={formData.id_bobina}
                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}
                placeholder="Es: 1, 2, A, B..."
                helperText="Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)"
              />
            </Grid>
          )}

          {dialogType === 'posa-periodo' && (
            <>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Inizio"
                  value={formData.data_inizio}
                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  type="date"
                  label="Data Fine"
                  value={formData.data_fine}
                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCloseDialog}>Annulla</Button>
        <Button
          onClick={handleGenerateReport}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}
        >
          {loading ? 'Generazione...' : 'Genera Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={() => navigate(-1)} color="primary">
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1">
            Report e Analytics
          </Typography>
        </Box>
        <AdminHomeButton />
      </Box>

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Reports */}
      <Box sx={{ mt: 3 }}>
        {/* Progress Report */}
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6">Report Avanzamento</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {reportsData.progress ? (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showCharts}
                        onChange={(e) => setShowCharts(e.target.checked)}
                        color="primary"
                      />
                    }
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ShowChartIcon sx={{ mr: 1 }} />
                        Mostra Grafici
                      </Box>
                    }
                  />
                  <Box>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'pdf')}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    >
                      PDF
                    </Button>
                    <Button
                      startIcon={<DownloadIcon />}
                      onClick={() => generateReportWithFormat('progress', 'excel')}
                      variant="outlined"
                      size="small"
                      color="success"
                    >
                      Excel
                    </Button>
                  </Box>
                </Box>
                {renderProgressReport(reportsData.progress)}
              </Box>
            ) : loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
              </Box>
            ) : (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  Impossibile caricare il report. Riprova più tardi.
                </Alert>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    setLoading(true);
                    reportService.getProgressReport(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          progress: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying progress report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                >
                  Riprova
                </Button>
              </Box>
            )}
          </AccordionDetails>
        </Accordion>

        {/* Bill of Quantities */}
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <ListIcon sx={{ mr: 1, color: 'secondary.main' }} />
              <Typography variant="h6">Bill of Quantities</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {reportsData.boq ? (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('boq', 'pdf')}
                    variant="outlined"
                    size="small"
                    color="primary"
                    sx={{ mr: 1 }}
                  >
                    PDF
                  </Button>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('boq', 'excel')}
                    variant="outlined"
                    size="small"
                    color="success"
                  >
                    Excel
                  </Button>
                </Box>
                {renderBoqReport(reportsData.boq)}
              </Box>
            ) : loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
              </Box>
            ) : (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  Impossibile caricare il report. Riprova più tardi.
                </Alert>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    setLoading(true);
                    reportService.getBillOfQuantities(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          boq: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying BOQ report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                >
                  Riprova
                </Button>
              </Box>
            )}
          </AccordionDetails>
        </Accordion>

        {/* Bobine Report */}
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <InventoryIcon sx={{ mr: 1, color: 'success.main' }} />
              <Typography variant="h6">Report Utilizzo Bobine</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {reportsData.bobine ? (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('bobine', 'pdf')}
                    variant="outlined"
                    size="small"
                    color="primary"
                    sx={{ mr: 1 }}
                  >
                    PDF
                  </Button>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('bobine', 'excel')}
                    variant="outlined"
                    size="small"
                    color="success"
                  >
                    Excel
                  </Button>
                </Box>
                {renderBobineReport(reportsData.bobine)}
              </Box>
            ) : loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
              </Box>
            ) : (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  Impossibile caricare il report. Riprova più tardi.
                </Alert>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    setLoading(true);
                    reportService.getBobineReport(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          bobine: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying bobine report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                >
                  Riprova
                </Button>
              </Box>
            )}
          </AccordionDetails>
        </Accordion>

        {/* Cavi Stato Report */}
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <BarChartIcon sx={{ mr: 1, color: 'error.main' }} />
              <Typography variant="h6">Report Cavi per Stato</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {reportsData.caviStato ? (
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}
                    variant="outlined"
                    size="small"
                    color="primary"
                    sx={{ mr: 1 }}
                  >
                    PDF
                  </Button>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('cavi-stato', 'excel')}
                    variant="outlined"
                    size="small"
                    color="success"
                  >
                    Excel
                  </Button>
                </Box>
                {renderCaviStatoReport(reportsData.caviStato)}
              </Box>
            ) : loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
                <CircularProgress size={24} />
                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>
              </Box>
            ) : (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  Impossibile caricare il report. Riprova più tardi.
                </Alert>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    setLoading(true);
                    reportService.getCaviStatoReport(cantiereId, 'video')
                      .then(data => {
                        setReportsData(prev => ({
                          ...prev,
                          caviStato: data.content
                        }));
                      })
                      .catch(err => {
                        console.error('Error retrying cavi stato report:', err);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }}
                >
                  Riprova
                </Button>
              </Box>
            )}
          </AccordionDetails>
        </Accordion>

        {/* Special Reports Section */}
        <Paper sx={{ p: 3, mt: 4 }}>
          <Typography variant="h6" gutterBottom>Report Speciali</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Questi report richiedono parametri aggiuntivi per essere generati.
          </Typography>

          <Grid container spacing={3}>
            {/* Bobina Specifica Report */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6">Report Bobina Specifica</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Dettaglio approfondito di una singola bobina con tutti i cavi associati.
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="info"
                    onClick={() => {
                      setDialogType('bobina-specifica');
                      setOpenDialog(true);
                    }}
                  >
                    Genera Report
                  </Button>
                </CardActions>
              </Card>
            </Grid>

            {/* Posa per Periodo Report */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6">Report Posa per Periodo</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Analisi temporale della posa con trend e pattern di lavoro.
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="warning"
                    onClick={() => {
                      setDialogType('posa-periodo');
                      // Set default date range (last month to today)
                      const today = new Date();
                      const lastMonth = new Date();
                      lastMonth.setMonth(today.getMonth() - 1);

                      setFormData({
                        ...formData,
                        data_inizio: lastMonth.toISOString().split('T')[0],
                        data_fine: today.toISOString().split('T')[0]
                      });
                      setOpenDialog(true);
                    }}
                  >
                    Genera Report
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>
        </Paper>

        {/* Display special reports if they exist */}
        {reportsData.bobinaSpecifica && (
          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CableIcon sx={{ mr: 1, color: 'info.main' }} />
                <Typography variant="h6">Report Bobina Specifica</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}
                    variant="outlined"
                    size="small"
                    color="primary"
                    sx={{ mr: 1 }}
                  >
                    PDF
                  </Button>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}
                    variant="outlined"
                    size="small"
                    color="success"
                  >
                    Excel
                  </Button>
                </Box>
                {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}
              </Box>
            </AccordionDetails>
          </Accordion>
        )}

        {reportsData.posaPeriodo && (
          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TimelineIcon sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="h6">Report Posa per Periodo</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}
                    variant="outlined"
                    size="small"
                    color="primary"
                    sx={{ mr: 1 }}
                  >
                    PDF
                  </Button>
                  <Button
                    startIcon={<DownloadIcon />}
                    onClick={() => generateReportWithFormat('posa-periodo', 'excel')}
                    variant="outlined"
                    size="small"
                    color="success"
                  >
                    Excel
                  </Button>
                </Box>
                {renderPosaPeriodoReport(reportsData.posaPeriodo)}
              </Box>
            </AccordionDetails>
          </Accordion>
        )}
      </Box>

      {/* Dialog per configurazione report */}
      {renderDialog()}
    </Box>
  );
};

export default ReportCaviPageNew;
