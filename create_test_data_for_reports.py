#!/usr/bin/env python3
"""
Script per creare dati di test per verificare i report del sistema CMS.
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Aggiungi il percorso dei moduli
sys.path.append('.')
from modules.database_pg import Database

def create_test_cantiere(id_utente):
    """Crea un cantiere di test."""
    db = Database()

    # Crea cantiere
    cantiere_id = 1
    db.execute_query("""
        INSERT INTO cantieri (id_cantiere, nome, descrizione, data_creazione, password_cantiere, id_utente)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON CONFLICT (id_cantiere) DO NOTHING
    """, (cantiere_id, "Cantiere Test Report", "Cantiere per test dei report", datetime.now(), "test123", id_utente))

    print(f"✅ Cantiere {cantiere_id} creato")
    return cantiere_id

def create_test_bobine(cantiere_id):
    """Crea bobine di test."""
    db = Database()

    bobine_data = [
        ("C1_B1", "FG7OR", "4x16", 1000, 800, "DISPONIBILE"),
        ("C1_B2", "FG7OR", "4x25", 1500, 1200, "IN_USO"),
        ("C1_B3", "N07V-K", "1x10", 500, 0, "TERMINATA"),
        ("C1_B4", "FG7OR", "4x16", 1000, 950, "DISPONIBILE"),
        ("C1_B5", "H07RN-F", "3x2.5", 300, 150, "IN_USO"),
        ("C1_B6", "FG7OR", "4x35", 2000, -50, "OVER"),  # Bobina in over
    ]

    for id_bobina, tipologia, sezione, metri_totali, metri_residui, stato in bobine_data:
        db.execute_query("""
            INSERT INTO parco_cavi (
                id_cantiere, id_bobina, tipologia, sezione,
                metri_totali, metri_residui, stato_bobina
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (id_cantiere, id_bobina) DO NOTHING
        """, (cantiere_id, id_bobina, tipologia, sezione, metri_totali, metri_residui, stato))

    print(f"✅ {len(bobine_data)} bobine create")

def create_test_cavi(cantiere_id):
    """Crea cavi di test."""
    db = Database()

    # Dati base per i cavi
    sistemi = ["Illuminazione", "Forza Motrice", "Controllo", "Sicurezza"]
    utilities = ["Principale", "Secondario", "Emergenza"]
    tipologie = ["FG7OR", "N07V-K", "H07RN-F"]
    sezioni = ["4x16", "4x25", "1x10", "3x2.5", "4x35"]
    stati = ["Installato", "Da Installare", "In Progresso"]

    # Crea 50 cavi di test
    for i in range(1, 51):
        id_cavo = f"CAV_{i:03d}"
        sistema = random.choice(sistemi)
        utility = random.choice(utilities)
        tipologia = random.choice(tipologie)
        sezione = random.choice(sezioni)
        metri_teorici = random.randint(50, 500)
        stato = random.choice(stati)

        # Se installato, aggiungi metratura reale e data posa
        if stato == "Installato":
            metratura_reale = metri_teorici + random.randint(-20, 50)  # Variazione realistica
            data_posa = datetime.now().date() - timedelta(days=random.randint(1, 30))
            # Assegna una bobina casuale
            bobine_ids = ["C1_B1", "C1_B2", "C1_B4", "C1_B5", "C1_B6"]
            id_bobina = random.choice(bobine_ids)
        else:
            metratura_reale = 0
            data_posa = None
            id_bobina = None

        db.execute_query("""
            INSERT INTO cavi (
                id_cantiere, id_cavo, sistema, utility, tipologia, sezione,
                metri_teorici, metratura_reale, stato_installazione,
                data_posa, id_bobina, modificato_manualmente
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (id_cantiere, id_cavo) DO NOTHING
        """, (cantiere_id, id_cavo, sistema, utility, tipologia, sezione,
              metri_teorici, metratura_reale, stato, data_posa, id_bobina, 0))

    print(f"✅ 50 cavi creati")

def create_test_users():
    """Crea utenti di test."""
    db = Database()

    # Admin user
    db.execute_query("""
        INSERT INTO users (username, password, role, is_active)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (username) DO NOTHING
    """, ("admin", "admin", "admin", True))

    # Standard user
    db.execute_query("""
        INSERT INTO users (username, password, role, is_active)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (username) DO NOTHING
    """, ("user1", "password", "user", True))

    # Ottieni l'ID dell'admin
    result = db.execute_query("SELECT id FROM users WHERE username = 'admin'")
    admin_id = result[0][0] if result else 1

    print("✅ Utenti di test creati")
    return admin_id

def verify_data():
    """Verifica che i dati siano stati creati correttamente."""
    db = Database()

    # Conta cantieri
    result = db.execute_query("SELECT COUNT(*) FROM cantieri")
    cantieri_count = result[0][0] if result else 0
    print(f"📊 Cantieri: {cantieri_count}")

    # Conta bobine
    result = db.execute_query("SELECT COUNT(*) FROM parco_cavi")
    bobine_count = result[0][0] if result else 0
    print(f"📊 Bobine: {bobine_count}")

    # Conta cavi
    result = db.execute_query("SELECT COUNT(*) FROM cavi")
    cavi_count = result[0][0] if result else 0
    print(f"📊 Cavi: {cavi_count}")

    # Conta cavi per stato
    result = db.execute_query("""
        SELECT stato_installazione, COUNT(*)
        FROM cavi
        GROUP BY stato_installazione
    """)
    if result:
        print("📊 Cavi per stato:")
        for row in result:
            print(f"   {row[0]}: {row[1]}")

    # Conta bobine per stato
    result = db.execute_query("""
        SELECT stato_bobina, COUNT(*)
        FROM parco_cavi
        GROUP BY stato_bobina
    """)
    if result:
        print("📊 Bobine per stato:")
        for row in result:
            print(f"   {row[0]}: {row[1]}")

def main():
    """Funzione principale."""
    print("🚀 Creazione dati di test per report CMS...")

    try:
        # Crea utenti prima
        admin_id = create_test_users()

        # Crea cantiere
        cantiere_id = create_test_cantiere(admin_id)

        # Crea bobine
        create_test_bobine(cantiere_id)

        # Crea cavi
        create_test_cavi(cantiere_id)

        # Verifica dati
        print("\n📊 Verifica dati creati:")
        verify_data()

        print("\n✅ Dati di test creati con successo!")
        print("🔗 Ora puoi testare i report all'URL: http://localhost:3000")
        print("👤 Login: admin / admin")

    except Exception as e:
        print(f"❌ Errore durante la creazione dei dati: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
